{"entries": [{"name": "ASSIMP_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "ASSIMP_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libassimp.so"}, {"name": "BUILD_GMOCK", "properties": [{"name": "HELPSTRING", "value": "Builds the googlemock subproject"}], "type": "BOOL", "value": "ON"}, {"name": "BUILD_SHARED_LIBS", "properties": [{"name": "HELPSTRING", "value": "Build dynamically-linked binaries"}], "type": "BOOL", "value": "ON"}, {"name": "BULLET_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/bullet"}, {"name": "BULLET_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "BULLET_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/bullet"}, {"name": "BULLET_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lBulletSoftBody;-lBulletDynamics;-lBulletCollision;-lLinearMath"}, {"name": "BULLET_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "BulletSoftBody;BulletDynamics;BulletCollision;LinearMath"}, {"name": "BULLET_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "bullet"}, {"name": "BULLET_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/bullet"}, {"name": "BULLET_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/bullet"}, {"name": "BULLET_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lBulletSoftBody;-lBulletDynamics;-lBulletCollision;-lLinearMath"}, {"name": "BULLET_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "BulletSoftBody;BulletDynamics;BulletCollision;LinearMath"}, {"name": "BULLET_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.88"}, {"name": "BULLET_bullet_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_bullet_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_bullet_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "BULLET_bullet_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "Boost_DATE_TIME_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0"}, {"name": "Boost_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Boost."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/Boost-1.71.0"}, {"name": "Boost_FILESYSTEM_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0"}, {"name": "Boost_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "Boost_IOSTREAMS_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.71.0"}, {"name": "Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0"}, {"name": "Boost_REGEX_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0"}, {"name": "Boost_SYSTEM_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0"}, {"name": "Boost_THREAD_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0"}, {"name": "CATKIN_BLACKLIST_PACKAGES", "properties": [{"name": "HELPSTRING", "value": "List of ';' separated packages to exclude"}], "type": "STRING", "value": ""}, {"name": "CATKIN_ENABLE_TESTING", "properties": [{"name": "HELPSTRING", "value": "Catkin enable testing"}], "type": "BOOL", "value": "ON"}, {"name": "CATKIN_ENV", "properties": [{"name": "HELPSTRING", "value": "catkin environment"}], "type": "INTERNAL", "value": "/home/<USER>/weednix_ws/src/build/catkin_generated/env_cached.sh"}, {"name": "CATKIN_SKIP_TESTING", "properties": [{"name": "HELPSTRING", "value": "<PERSON><PERSON> skip testing"}], "type": "BOOL", "value": "OFF"}, {"name": "CATKIN_SYMLINK_INSTALL", "properties": [{"name": "HELPSTRING", "value": "Replace the CMake install command with a custom implementation using symlinks instead of copying resources"}], "type": "BOOL", "value": "OFF"}, {"name": "CATKIN_TEST_RESULTS_DIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/home/<USER>/weednix_ws/src/build/test_results"}, {"name": "CATKIN_WHITELIST_PACKAGES", "properties": [{"name": "HELPSTRING", "value": "List of ';' separated packages to build"}], "type": "STRING", "value": ""}, {"name": "CCD_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "CCD_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libccd.so"}, {"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/addr2line"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/home/<USER>/weednix_ws/src/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "16"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_COLOR_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable color output during build."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/usr/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/usr/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/usr/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "FILEPATH", "value": "/usr/bin/c++"}, {"name": "CMAKE_CXX_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ar-9"}, {"name": "CMAKE_CXX_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ranlib-9"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/gcc-8"}, {"name": "CMAKE_C_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ar-8"}, {"name": "CMAKE_C_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ranlib-8"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "ELF"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Unix Makefiles"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREADS_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthreads"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREAD_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthread"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HAVE_PTHREAD_H", "properties": [{"name": "HELPSTRING", "value": "Have include pthread.h"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/home/<USER>/weednix_ws/src"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SO_NO_EXE", "properties": [{"name": "HELPSTRING", "value": "Install .so files without execute permission."}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ld"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/make"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "11"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objcopy"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objdump"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "Project"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1.10.0"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "10"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/readelf"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/usr/share/cmake-3.16"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/strip"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "CPPZMQ_INCLUDE_DIRS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "CURL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "CURL_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CURL_LIBRARY_DEBUG-NOTFOUND"}, {"name": "CURL_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libcurl.so"}, {"name": "DART_DEFINITIONS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "DART_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for DART."}], "type": "PATH", "value": "/usr/share/dart/cmake"}, {"name": "DART_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "DART_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "dart;dart;dart;dart;dart;dart;dart"}, {"name": "DART_MIN_BOOST_VERSION", "properties": [{"name": "HELPSTRING", "value": "Boost min version requirement"}], "type": "INTERNAL", "value": "1.58.0"}, {"name": "DART_dart_DEFINITIONS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "DART_dart_DEPENDENCIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "external-odelcpsolver"}, {"name": "DART_dart_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "TRUE"}, {"name": "DART_dart_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "DART_dart_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "dart"}, {"name": "DL_INCLUDE_DIRS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "DL_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libdl.so"}, {"name": "DOXYGEN_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "DOXYGEN_EXECUTABLE-NOTFOUND"}, {"name": "EIGEN3_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/eigen3"}, {"name": "EIGEN3_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "EIGEN3_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/eigen3"}, {"name": "EIGEN3_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "eigen3"}, {"name": "EIGEN3_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "EIGEN3_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/eigen3"}, {"name": "EIGEN3_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/eigen3"}, {"name": "EIGEN3_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "3.3.7"}, {"name": "EIGEN3_eigen3_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_eigen3_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_eigen3_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EIGEN3_eigen3_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "EMPY_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "EMPY_EXECUTABLE-NOTFOUND"}, {"name": "EMPY_SCRIPT", "properties": [{"name": "HELPSTRING", "value": "Empy script"}], "type": "STRING", "value": "/usr/lib/python3/dist-packages/em.py"}, {"name": "Eigen3_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Eigen3."}], "type": "PATH", "value": "/usr/lib/cmake/eigen3"}, {"name": "FCL_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/opt/ros/noetic/include"}, {"name": "FCL_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/ros/noetic/lib/x86_64-linux-gnu/libfcl.so"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Boost", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/cmake/Boost-1.71.0/BoostConfig.cmake][cfound components: thread system filesystem program_options regex iostreams date_time ][v1.71.0(1.40.0)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_CURL", "properties": [{"name": "HELPSTRING", "value": "Details about finding CURL"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/libcurl.so][/usr/include/x86_64-linux-gnu][c ][v7.68.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_DART", "properties": [{"name": "HELPSTRING", "value": "Details about finding DART"}], "type": "INTERNAL", "value": "[/usr/include][dart;dart;dart;dart;dart;dart;dart][cfound components: dart ][v(6.6)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_DL", "properties": [{"name": "HELPSTRING", "value": "Details about finding DL"}], "type": "INTERNAL", "value": "[true][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_JSONCPP", "properties": [{"name": "HELPSTRING", "value": "Details about finding JSONCPP"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenCV", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenCV"}], "type": "INTERNAL", "value": "[/usr][v4.2.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PY_em", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>_<PERSON>"}], "type": "INTERNAL", "value": "[/usr/lib/python3/dist-packages/em.py][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[/usr/bin/pkg-config][v0.29.1()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Protobuf", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON><PERSON>"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/libprotobuf.so;-lpthread][/usr/include][v3.6.1(3)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp", "properties": [{"name": "HELPSTRING", "value": "Details about finding PythonInterp"}], "type": "INTERNAL", "value": "[/usr/bin/python3][v3.8.10()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_UUID", "properties": [{"name": "HELPSTRING", "value": "Details about finding UUID"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_YAML", "properties": [{"name": "HELPSTRING", "value": "Details about finding YAML"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ZIP", "properties": [{"name": "HELPSTRING", "value": "Details about finding ZIP"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ZeroMQ", "properties": [{"name": "HELPSTRING", "value": "Details about finding ZeroMQ"}], "type": "INTERNAL", "value": "[TRUE][v(4)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_assimp", "properties": [{"name": "HELPSTRING", "value": "Details about finding assimp"}], "type": "INTERNAL", "value": "[/usr/include][/usr/lib/x86_64-linux-gnu/libassimp.so][v5.0.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ccd", "properties": [{"name": "HELPSTRING", "value": "Details about finding ccd"}], "type": "INTERNAL", "value": "[/usr/include][/usr/lib/x86_64-linux-gnu/libccd.so][v2.0(2.0)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_fcl", "properties": [{"name": "HELPSTRING", "value": "Details about finding fcl"}], "type": "INTERNAL", "value": "[/opt/ros/noetic/include][/opt/ros/noetic/lib/x86_64-linux-gnu/libfcl.so][v0.6.1(0.3.2)]"}, {"name": "GMOCK_FROM_SOURCE_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "TRUE"}, {"name": "GMOCK_FROM_SOURCE_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/src/googletest/googlemock/include"}, {"name": "GMOCK_FROM_SOURCE_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gmock"}, {"name": "GMOCK_FROM_SOURCE_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/home/<USER>/weednix_ws/src/build/gmock"}, {"name": "GMOCK_FROM_SOURCE_MAIN_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gmock_main"}, {"name": "GMOCK_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "GMOCK_LIBRARY-NOTFOUND"}, {"name": "GMOCK_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "GMOCK_LIBRARY_DEBUG-NOTFOUND"}, {"name": "GMOCK_MAIN_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "GMOCK_MAIN_LIBRARY-NOTFOUND"}, {"name": "GMOCK_MAIN_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "GMOCK_MAIN_LIBRARY_DEBUG-NOTFOUND"}, {"name": "GMock_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for GMock."}], "type": "PATH", "value": "GMock_DIR-NOTFOUND"}, {"name": "GTEST_FROM_SOURCE_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "TRUE"}, {"name": "GTEST_FROM_SOURCE_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "GTEST_FROM_SOURCE_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gtest"}, {"name": "GTEST_FROM_SOURCE_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/home/<USER>/weednix_ws/src/build/gtest"}, {"name": "GTEST_FROM_SOURCE_MAIN_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gtest_main"}, {"name": "GTEST_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "GTEST_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libgtest.a"}, {"name": "GTEST_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "GTEST_LIBRARY_DEBUG-NOTFOUND"}, {"name": "GTEST_MAIN_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libgtest_main.a"}, {"name": "GTEST_MAIN_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "GTEST_MAIN_LIBRARY_DEBUG-NOTFOUND"}, {"name": "GTest_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for GTest."}], "type": "PATH", "value": "GTest_DIR-NOTFOUND"}, {"name": "GZ_SANITIZER", "properties": [{"name": "HELPSTRING", "value": "Compile with a sanitizer. Options are: Address, Memory, MemoryWithOrigins, Undefined, Thread, Leak, 'Address;Undefined', CFI"}], "type": "STRING", "value": ""}, {"name": "IGN_SANITIZER", "properties": [{"name": "HELPSTRING", "value": "Compile with a sanitizer. Options are: Address, Memory, MemoryWithOrigins, Undefined, Thread, Leak, 'Address;Undefined', CFI"}], "type": "STRING", "value": ""}, {"name": "INSTALL_GTEST", "properties": [{"name": "HELPSTRING", "value": "Enable installation of googletest. (Projects embedding googletest may want to turn this OFF.)"}], "type": "BOOL", "value": "OFF"}, {"name": "JSONCPP_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/jsoncpp"}, {"name": "JSONCPP_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/jsoncpp"}, {"name": "JSONCPP_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/jsoncpp"}, {"name": "JSONCPP_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lj<PERSON><PERSON><PERSON>"}, {"name": "JSONCPP_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "JSONCPP_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "jsoncpp"}, {"name": "JSONCPP_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_LIBRARY_jsoncpp", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libjsoncpp.so"}, {"name": "JSONCPP_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "jsoncpp"}, {"name": "JSONCPP_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "JSONCPP_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/jsoncpp"}, {"name": "JSONCPP_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/jsoncpp"}, {"name": "JSONCPP_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lj<PERSON><PERSON><PERSON>"}, {"name": "JSONCPP_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "jsoncpp"}, {"name": "JSONCPP_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1.7.4"}, {"name": "JSONCPP_jsoncpp_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_jsoncpp_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_jsoncpp_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "JSONCPP_jsoncpp_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "LSB_FOUND", "properties": [{"name": "HELPSTRING", "value": "lsb_release executable was found"}], "type": "BOOL", "value": "TRUE"}, {"name": "LSB_RELEASE_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/lsb_release"}, {"name": "NOSETESTS", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/nosetests3"}, {"name": "OGRE_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_CONFIG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE"}, {"name": "OGRE_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE"}, {"name": "OGRE_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreMain.so"}, {"name": "OGRE_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreMain.so"}, {"name": "OGRE_MEDIA_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "OGRE_MEDIA_DIR-NOTFOUND"}, {"name": "OGRE_Overlay_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Overlay_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Overlay_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Overlay"}, {"name": "OGRE_Overlay_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Overlay_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreOverlay.so"}, {"name": "OGRE_Overlay_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Overlay_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreOverlay.so"}, {"name": "OGRE_PKGC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread;-I/usr/include/OGRE"}, {"name": "OGRE_PKGC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "OGRE_PKGC_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "OGRE_PKGC_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "OGRE_PKGC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/OGRE"}, {"name": "OGRE_PKGC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lOgre<PERSON>ain;-lpthread"}, {"name": "OGRE_PKGC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "OGRE_PKGC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "OgreMain;pthread"}, {"name": "OGRE_PKGC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "OGRE"}, {"name": "OGRE_PKGC_OGRE_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_OGRE_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_OGRE_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_OGRE_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "OGRE_PKGC_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread;-I/usr/include/OGRE"}, {"name": "OGRE_PKGC_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "OGRE_PKGC_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/OGRE"}, {"name": "OGRE_PKGC_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lOgre<PERSON>ain;-lpthread"}, {"name": "OGRE_PKGC_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "OgreMain;pthread"}, {"name": "OGRE_PKGC_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "OGRE_PKGC_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1.9.0"}, {"name": "OGRE_PLUGIN_DIR_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Ogre plugin dir (debug)"}], "type": "STRING", "value": ""}, {"name": "OGRE_PLUGIN_DIR_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Ogre plugin dir (release)"}], "type": "STRING", "value": ""}, {"name": "OGRE_PREFIX_WATCH_INT_CHECK", "properties": [{"name": "HELPSTRING", "value": "x"}], "type": "INTERNAL", "value": "/opt/ogre;/opt/OGRE;/usr/lib/ogre;/usr/lib/OGRE;/usr/local/lib/ogre;/usr/local/lib/OGRE;/home/<USER>/ogre;/home/<USER>/OGRE;NOTFOUND;NOTFOUND"}, {"name": "OGRE_Paging_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Paging_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Paging_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Paging"}, {"name": "OGRE_Paging_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Paging_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgrePaging.so"}, {"name": "OGRE_Paging_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Paging_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgrePaging.so"}, {"name": "OGRE_Plugin_BSPSceneManager_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_BSPSceneManager_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Plugins/BSPSceneManager"}, {"name": "OGRE_Plugin_BSPSceneManager_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_BSPSceneManager_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_Plugin_BSPSceneManager_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Plugin_BSPSceneManager_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_BSPSceneManager_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_Plugin_BSPSceneManager_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_CgProgramManager_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "OGRE_Plugin_CgProgramManager_INCLUDE_DIR-NOTFOUND"}, {"name": "OGRE_Plugin_CgProgramManager_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_CgProgramManager_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_Plugin_CgProgramManager_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Plugin_CgProgramManager_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_CgProgramManager_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_Plugin_OctreeSceneManager_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_OctreeSceneManager_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Plugins/OctreeSceneManager"}, {"name": "OGRE_Plugin_OctreeSceneManager_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_OctreeSceneManager_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_Plugin_OctreeSceneManager_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Plugin_OctreeSceneManager_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_OctreeSceneManager_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_Plugin_OctreeSceneManager_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_OctreeZone_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_OctreeZone_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Plugins/OctreeZone"}, {"name": "OGRE_Plugin_OctreeZone_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_OctreeZone_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_Plugin_OctreeZone_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Plugin_OctreeZone_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_OctreeZone_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_Plugin_OctreeZone_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_PCZSceneManager_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_PCZSceneManager_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Plugins/PCZSceneManager"}, {"name": "OGRE_Plugin_PCZSceneManager_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_PCZSceneManager_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_Plugin_PCZSceneManager_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Plugin_PCZSceneManager_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_PCZSceneManager_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_Plugin_PCZSceneManager_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_ParticleFX_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Plugin_ParticleFX_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Plugins/ParticleFX"}, {"name": "OGRE_Plugin_ParticleFX_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_ParticleFX_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_Plugin_ParticleFX_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Plugin_ParticleFX_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_Plugin_ParticleFX_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_Plugin_ParticleFX_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Property_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Property_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Property_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include/OGRE/Property"}, {"name": "OGRE_Property_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Property_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreProperty.so"}, {"name": "OGRE_Property_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Property_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreProperty.so"}, {"name": "OGRE_RTShaderSystem_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RTShaderSystem_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RTShaderSystem_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include/OGRE/RTShaderSystem"}, {"name": "OGRE_RTShaderSystem_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RTShaderSystem_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreRTShaderSystem.so"}, {"name": "OGRE_RTShaderSystem_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RTShaderSystem_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreRTShaderSystem.so"}, {"name": "OGRE_RenderSystem_Direct3D11_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "OGRE_RenderSystem_Direct3D11_INCLUDE_DIR-NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D11_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_Direct3D11_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D11_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D11_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_Direct3D11_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D9_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "OGRE_RenderSystem_Direct3D9_INCLUDE_DIR-NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D9_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_Direct3D9_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D9_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_RenderSystem_Direct3D9_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_Direct3D9_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_RenderSystem_GL3Plus_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "OGRE_RenderSystem_GL3Plus_INCLUDE_DIR-NOTFOUND"}, {"name": "OGRE_RenderSystem_GL3Plus_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GL3Plus_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_RenderSystem_GL3Plus_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_RenderSystem_GL3Plus_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GL3Plus_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES2_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RenderSystem_GLES2_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/RenderSystems/GLES2"}, {"name": "OGRE_RenderSystem_GLES2_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GLES2_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES2_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES2_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GLES2_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES2_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RenderSystem_GLES_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "OGRE_RenderSystem_GLES_INCLUDE_DIR-NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GLES_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_RenderSystem_GLES_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GLES_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_RenderSystem_GL_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_RenderSystem_GL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/RenderSystems/GL"}, {"name": "OGRE_RenderSystem_GL_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GL_LIBRARY_DBG-NOTFOUND"}, {"name": "OGRE_RenderSystem_GL_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_RenderSystem_GL_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "OGRE_RenderSystem_GL_LIBRARY_REL-NOTFOUND"}, {"name": "OGRE_RenderSystem_GL_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Terrain_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Terrain_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Terrain_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Terrain"}, {"name": "OGRE_Terrain_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Terrain_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreTerrain.so"}, {"name": "OGRE_Terrain_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Terrain_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreTerrain.so"}, {"name": "OGRE_Volume_BINARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Volume_BINARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Volume_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "PATH", "value": "/usr/include/OGRE/Volume"}, {"name": "OGRE_Volume_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "OGRE_Volume_LIBRARY_DBG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreVolume.so"}, {"name": "OGRE_Volume_LIBRARY_FWK", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OGRE_Volume_LIBRARY_REL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "x"}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreVolume.so"}, {"name": "OpenCV_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCV."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/opencv4"}, {"name": "PC_ASSIMP_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/../include/include"}, {"name": "PC_ASSIMP_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PC_ASSIMP_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/../include/include"}, {"name": "PC_ASSIMP_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/../include/include"}, {"name": "PC_ASSIMP_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lassi<PERSON>"}, {"name": "PC_ASSIMP_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "PC_ASSIMP_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "assimp"}, {"name": "PC_ASSIMP_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "assimp"}, {"name": "PC_ASSIMP_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "PC_ASSIMP_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/../include/include"}, {"name": "PC_ASSIMP_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/../include/include"}, {"name": "PC_ASSIMP_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lassimp;-lstdc++;-lz"}, {"name": "PC_ASSIMP_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "assimp;stdc++;z"}, {"name": "PC_ASSIMP_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "5.0.0"}, {"name": "PC_ASSIMP_assimp_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_assimp_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_assimp_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_ASSIMP_assimp_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PC_CCD_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "PC_CCD_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lccd;-lm"}, {"name": "PC_CCD_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "PC_CCD_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ccd;m"}, {"name": "PC_CCD_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ccd"}, {"name": "PC_CCD_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "PC_CCD_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lccd;-lm"}, {"name": "PC_CCD_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ccd;m"}, {"name": "PC_CCD_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.0"}, {"name": "PC_CCD_ccd_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_ccd_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_ccd_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CCD_ccd_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "PC_CURL_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PC_CURL_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "PC_CURL_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "PC_CURL_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lcurl"}, {"name": "PC_CURL_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "PC_CURL_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "curl"}, {"name": "PC_CURL_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libcurl"}, {"name": "PC_CURL_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "PC_CURL_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "PC_CURL_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "PC_CURL_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu/mit-krb5;-lcurl;-lnghttp2;-lidn2;-lrtmp;-lssh;-lpsl;-lssl;-lcrypto;-lssl;-lcrypto;-Wl,-Bsymbolic-functions;-Wl,-z,relro;-lgssapi_krb5;-lkrb5;-lk5crypto;-lcom_err;-llber;-lldap;-llber;-lbrotlidec;-lz"}, {"name": "PC_CURL_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-Wl,-Bsymbolic-functions;-Wl,-z,relro"}, {"name": "PC_CURL_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "curl;nghttp2;idn2;rtmp;ssh;psl;ssl;crypto;ssl;crypto;gssapi_krb5;krb5;k5crypto;com_err;lber;ldap;lber;brotlidec;z"}, {"name": "PC_CURL_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu/mit-krb5"}, {"name": "PC_CURL_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "7.68.0"}, {"name": "PC_CURL_libcurl_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_libcurl_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_libcurl_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_CURL_libcurl_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-std=c++11;-I/opt/ros/noetic/include;-I/usr/include/eigen3"}, {"name": "PC_FCL_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-std=c++11"}, {"name": "PC_FCL_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PC_FCL_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic/include"}, {"name": "PC_FCL_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic/include;/usr/include/eigen3"}, {"name": "PC_FCL_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/opt/ros/noetic/lib;-L/opt/ros/noetic/lib/x86_64-linux-gnu;-lfcl;-lccd;-lm;-loctomap;-loctomath"}, {"name": "PC_FCL_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic/lib/x86_64-linux-gnu"}, {"name": "PC_FCL_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "fcl;ccd;m;octomap;octomath"}, {"name": "PC_FCL_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic/lib;/opt/ros/noetic/lib/x86_64-linux-gnu"}, {"name": "PC_FCL_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "fcl"}, {"name": "PC_FCL_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic"}, {"name": "PC_FCL_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-std=c++11;-I/opt/ros/noetic/include;-I/usr/include/eigen3"}, {"name": "PC_FCL_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-std=c++11"}, {"name": "PC_FCL_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic/include;/usr/include/eigen3"}, {"name": "PC_FCL_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/opt/ros/noetic/lib;-L/opt/ros/noetic/lib/x86_64-linux-gnu;-lfcl;-lccd;-lm;-loctomap;-loctomath"}, {"name": "PC_FCL_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "fcl;ccd;m;octomap;octomath"}, {"name": "PC_FCL_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/ros/noetic/lib;/opt/ros/noetic/lib/x86_64-linux-gnu"}, {"name": "PC_FCL_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "0.6.1"}, {"name": "PC_FCL_fcl_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_fcl_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_fcl_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_FCL_fcl_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/usr/bin/pkg-config"}, {"name": "PYTHON_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/python3"}, {"name": "PYTHON_INSTALL_DIR", "properties": [{"name": "HELPSTRING", "value": "This needs to be in PYTHONPATH when 'setup.py install' is called.  And it needs to match.  But setuptools won't tell us where it will install things."}], "type": "INTERNAL", "value": "lib/python3/dist-packages"}, {"name": "PYTHON_VERSION", "properties": [{"name": "HELPSTRING", "value": "Specify specific Python version to use ('major.minor' or 'major')"}], "type": "STRING", "value": "3"}, {"name": "PY_EM", "properties": [{"name": "HELPSTRING", "value": "Location of Python module em"}], "type": "STRING", "value": "/usr/lib/python3/dist-packages/em.py"}, {"name": "Project_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build"}, {"name": "Project_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src"}, {"name": "Protobuf_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Protobuf."}], "type": "PATH", "value": "Protobuf_DIR-NOTFOUND"}, {"name": "Protobuf_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "Protobuf_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libprotobuf.so"}, {"name": "Protobuf_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libprotobuf.so"}, {"name": "Protobuf_LITE_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libprotobuf-lite.so"}, {"name": "Protobuf_LITE_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libprotobuf-lite.so"}, {"name": "Protobuf_PROTOC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The Google Protocol Buffers Compiler"}], "type": "FILEPATH", "value": "/usr/bin/protoc"}, {"name": "Protobuf_PROTOC_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libprotoc.so"}, {"name": "Protobuf_PROTOC_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libprotoc.so"}, {"name": "RT_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/librt.so"}, {"name": "SETUPTOOLS_DEB_LAYOUT", "properties": [{"name": "HELPSTRING", "value": "Enable debian style python package layout"}], "type": "BOOL", "value": "ON"}, {"name": "SITE", "properties": [{"name": "HELPSTRING", "value": "Name of the computer/site where compile is being run"}], "type": "STRING", "value": "abdulaziz-Latitude-E5550"}, {"name": "Simbody_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Simbody."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/simbody"}, {"name": "Simbody_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "Simbody_STATIC_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "(This variable does not exist and should not be used)"}], "type": "UNINITIALIZED", "value": ""}, {"name": "TINYXML2_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "TINYXML2_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-ltinyxml2"}, {"name": "TINYXML2_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "TINYXML2_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "tinyxml2"}, {"name": "TINYXML2_LIBRARY_/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "TINYXML2_LIBRARY_/usr/lib/x86_64-linux-gnu/libtinyxml2.so-NOTFOUND"}, {"name": "TINYXML2_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_LIBRARY_tinyxml2", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so"}, {"name": "TINYXML2_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "tinyxml2"}, {"name": "TINYXML2_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "TINYXML2_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-ltinyxml2"}, {"name": "TINYXML2_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "tinyxml2"}, {"name": "TINYXML2_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "6.2.0"}, {"name": "TINYXML2_tinyxml2_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_tinyxml2_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_tinyxml2_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "TINYXML2_tinyxml2_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UBUNTU", "properties": [{"name": "HELPSTRING", "value": "LSB Distrib tag"}], "type": "BOOL", "value": "TRUE"}, {"name": "UBUNTU_FOCAL", "properties": [{"name": "HELPSTRING", "value": "LSB Distrib - codename tag"}], "type": "BOOL", "value": "TRUE"}, {"name": "UUID_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/uuid"}, {"name": "UUID_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "UUID_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/uuid"}, {"name": "UUID_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lu<PERSON>"}, {"name": "UUID_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "UUID_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "uuid"}, {"name": "UUID_LIBRARY_/usr/lib/x86_64-linux-gnu/libuuid.so", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "UUID_LIBRARY_/usr/lib/x86_64-linux-gnu/libuuid.so-NOTFOUND"}, {"name": "UUID_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_LIBRARY_uuid", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libuuid.so"}, {"name": "UUID_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "uuid"}, {"name": "UUID_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "UUID_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/uuid"}, {"name": "UUID_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/uuid"}, {"name": "UUID_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lu<PERSON>"}, {"name": "UUID_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "uuid"}, {"name": "UUID_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.34.0"}, {"name": "UUID_uuid_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_uuid_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_uuid_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "UUID_uuid_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "YAML_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-l<PERSON>l"}, {"name": "YAML_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "YAML_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "yaml"}, {"name": "YAML_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_LIBRARY_yaml", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libyaml.so"}, {"name": "YAML_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "yaml-0.1"}, {"name": "YAML_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "YAML_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-l<PERSON>l"}, {"name": "YAML_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "yaml"}, {"name": "YAML_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "0.2.2"}, {"name": "YAML_yaml-0.1_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_yaml-0.1_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_yaml-0.1_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "YAML_yaml-0.1_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "ZIP_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr//usr/lib/x86_64-linux-gnu;-lzip"}, {"name": "ZIP_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr//usr/lib/x86_64-linux-gnu"}, {"name": "ZIP_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "zip"}, {"name": "ZIP_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr//usr/lib/x86_64-linux-gnu"}, {"name": "ZIP_LIBRARY_zip", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libzip.so"}, {"name": "ZIP_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libzip"}, {"name": "ZIP_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "ZIP_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr//usr/lib/x86_64-linux-gnu;-lzip;-lbz2;-lz"}, {"name": "ZIP_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "zip;bz2;z"}, {"name": "ZIP_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr//usr/lib/x86_64-linux-gnu"}, {"name": "ZIP_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1.5.1"}, {"name": "ZIP_libzip_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_libzip_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_libzip_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZIP_libzip_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-isystem;/usr/include/mit-krb5;-I/usr/include/pgm-5.2"}, {"name": "ZeroMQ_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-isystem;/usr/include/mit-krb5"}, {"name": "ZeroMQ_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ZeroMQ."}], "type": "PATH", "value": "ZeroMQ_DIR-NOTFOUND"}, {"name": "ZeroMQ_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "ZeroMQ_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/pgm-5.2"}, {"name": "ZeroMQ_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-lzmq"}, {"name": "ZeroMQ_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "ZeroMQ_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "zmq"}, {"name": "ZeroMQ_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_LIBRARY_zmq", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libzmq.so"}, {"name": "ZeroMQ_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libzmq "}, {"name": "ZeroMQ_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "ZeroMQ_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-isystem;/usr/include/mit-krb5;-I/usr/include/pgm-5.2"}, {"name": "ZeroMQ_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-isystem;/usr/include/mit-krb5"}, {"name": "ZeroMQ_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/pgm-5.2"}, {"name": "ZeroMQ_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu/mit-krb5;-lzmq;-lstdc++;-lpthread;-lrt;-lgssapi_krb5;-lkrb5;-lk5crypto;-lcom_err;-lkrb5support;-lsodium;-pthread;-lpgm;-lpthread;-lm;-lnorm;-lstdc++;-lprotolib"}, {"name": "ZeroMQ_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "ZeroMQ_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "zmq;stdc++;pthread;rt;gssapi_krb5;krb5;k5crypto;com_err;krb5support;sodium;pgm;pthread;m;norm;stdc++;protolib"}, {"name": "ZeroMQ_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu/mit-krb5"}, {"name": "ZeroMQ_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "4.3.2"}, {"name": "ZeroMQ_libzmq _INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_libzmq _LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_libzmq _PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "ZeroMQ_libzmq _VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "/usr/local"}, {"name": "__pkg_config_arguments_BULLET", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "bullet>=2.82"}, {"name": "__pkg_config_arguments_EIGEN3", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;eigen3"}, {"name": "__pkg_config_arguments_JSONCPP", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "jsoncpp"}, {"name": "__pkg_config_arguments_OGRE_PKGC", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "OGRE"}, {"name": "__pkg_config_arguments_PC_ASSIMP", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "assimp;QUIET"}, {"name": "__pkg_config_arguments_PC_CCD", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ccd;QUIET"}, {"name": "__pkg_config_arguments_PC_CURL", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "QUIET;libcurl"}, {"name": "__pkg_config_arguments_PC_FCL", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "fcl;QUIET"}, {"name": "__pkg_config_arguments_TINYXML2", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "tinyxml2"}, {"name": "__pkg_config_arguments_UUID", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "uuid"}, {"name": "__pkg_config_arguments_YAML", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "yaml-0.1"}, {"name": "__pkg_config_arguments_ZIP", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libzip"}, {"name": "__pkg_config_arguments_ZeroMQ", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libzmq >= 4"}, {"name": "__pkg_config_checked_BULLET", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_EIGEN3", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_JSONCPP", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_OGRE_PKGC", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_PC_ASSIMP", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_PC_CCD", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_PC_CURL", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_PC_FCL", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_TINYXML2", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_UUID", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_YAML", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_ZIP", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_ZeroMQ", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "_gmock_INCLUDES", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "FILEPATH", "value": "/usr/src/googletest/googlemock/include/gmock/gmock.h"}, {"name": "_gmock_SOURCES", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "FILEPATH", "value": "/usr/src/gmock/src/gmock.cc"}, {"name": "_gtest_INCLUDES", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "FILEPATH", "value": "/usr/include/gtest/gtest.h"}, {"name": "_gtest_SOURCES", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "FILEPATH", "value": "/usr/src/gtest/src/gtest.cc"}, {"name": "actionlib_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for actionlib."}], "type": "PATH", "value": "/opt/ros/noetic/share/actionlib/cmake"}, {"name": "actionlib_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for actionlib_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/actionlib_msgs/cmake"}, {"name": "boost_atomic_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_atomic."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_atomic-1.71.0"}, {"name": "boost_date_time_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_date_time."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_date_time-1.71.0"}, {"name": "boost_filesystem_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_filesystem."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.71.0"}, {"name": "boost_headers_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_headers."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.71.0"}, {"name": "boost_iostreams_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_iostreams."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_iostreams-1.71.0"}, {"name": "boost_program_options_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_program_options."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_program_options-1.71.0"}, {"name": "boost_regex_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_regex."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_regex-1.71.0"}, {"name": "boost_system_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_system."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.71.0"}, {"name": "boost_thread_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_thread."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/boost_thread-1.71.0"}, {"name": "camera_calibration_parsers_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for camera_calibration_parsers."}], "type": "PATH", "value": "/opt/ros/noetic/share/camera_calibration_parsers/cmake"}, {"name": "camera_info_manager_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for camera_info_manager."}], "type": "PATH", "value": "/opt/ros/noetic/share/camera_info_manager/cmake"}, {"name": "catkin_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for catkin."}], "type": "PATH", "value": "/opt/ros/noetic/share/catkin/cmake"}, {"name": "ccd_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ccd."}], "type": "PATH", "value": "ccd_DIR-NOTFOUND"}, {"name": "class_loader_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for class_loader."}], "type": "PATH", "value": "/opt/ros/noetic/share/class_loader/cmake"}, {"name": "cmake_modules_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for cmake_modules."}], "type": "PATH", "value": "/opt/ros/noetic/share/cmake_modules/cmake"}, {"name": "cpp_common_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for cpp_common."}], "type": "PATH", "value": "/opt/ros/noetic/share/cpp_common/cmake"}, {"name": "cv_bridge_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for cv_bridge."}], "type": "PATH", "value": "/opt/ros/noetic/share/cv_bridge/cmake"}, {"name": "dynamic_reconfigure_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for dynamic_reconfigure."}], "type": "PATH", "value": "/opt/ros/noetic/share/dynamic_reconfigure/cmake"}, {"name": "gazebo_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for gazebo."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/gazebo"}, {"name": "gazebo_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for gazebo_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/gazebo_msgs/cmake"}, {"name": "gazebo_proto_msgs_lib", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libgazebo_msgs.so"}, {"name": "gazebo_ros_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for gazebo_ros."}], "type": "PATH", "value": "/opt/ros/noetic/share/gazebo_ros/cmake"}, {"name": "gencpp_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for gencpp."}], "type": "PATH", "value": "/opt/ros/noetic/share/gencpp/cmake"}, {"name": "geneus_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for geneus."}], "type": "PATH", "value": "/opt/ros/noetic/share/geneus/cmake"}, {"name": "genlisp_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for genlisp."}], "type": "PATH", "value": "/opt/ros/noetic/share/genlisp/cmake"}, {"name": "genmsg_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for genmsg."}], "type": "PATH", "value": "/opt/ros/noetic/share/genmsg/cmake"}, {"name": "gennodejs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for gennodejs."}], "type": "PATH", "value": "/opt/ros/noetic/share/gennodejs/cmake"}, {"name": "genpy_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for genpy."}], "type": "PATH", "value": "/opt/ros/noetic/share/genpy/cmake"}, {"name": "geometry_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for geometry_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/geometry_msgs/cmake"}, {"name": "gmock_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/gtest/googlemock"}, {"name": "gmock_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;gtest;"}, {"name": "gmock_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/usr/src/googletest/googlemock"}, {"name": "gmock_build_tests", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Build all of Google Mock's own tests."}], "type": "BOOL", "value": "OFF"}, {"name": "gmock_main_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;gmock;"}, {"name": "googletest-distribution_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/gtest"}, {"name": "googletest-distribution_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/usr/src/googletest"}, {"name": "gtest_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/gtest/googletest"}, {"name": "gtest_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/usr/src/googletest/googletest"}, {"name": "gtest_build_samples", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Build gtest's sample programs."}], "type": "BOOL", "value": "OFF"}, {"name": "gtest_build_tests", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Build all of gtest's own tests."}], "type": "BOOL", "value": "OFF"}, {"name": "gtest_disable_pthreads", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Disable uses of pthreads in gtest."}], "type": "BOOL", "value": "OFF"}, {"name": "gtest_force_shared_crt", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Use shared (DLL) run-time lib even when Google Test is built as static lib."}], "type": "BOOL", "value": "OFF"}, {"name": "gtest_hide_internal_symbols", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Build gtest with internal symbols hidden in shared libraries."}], "type": "BOOL", "value": "OFF"}, {"name": "gtest_main_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;gtest;"}, {"name": "ignition-cmake2_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-cmake2."}], "type": "PATH", "value": "/usr/share/cmake/ignition-cmake2"}, {"name": "ignition-common3-graphics_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-common3-graphics."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/ignition-common3-graphics"}, {"name": "ignition-common3_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-common3."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/ignition-common3"}, {"name": "ignition-fuel_tools4_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-fuel_tools4."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/ignition-fuel_tools4"}, {"name": "ignition-math6_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-math6."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/ignition-math6"}, {"name": "ignition-msgs5_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-msgs5."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/ignition-msgs5"}, {"name": "ignition-transport8_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for ignition-transport8."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/ignition-transport8"}, {"name": "image_transport_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for image_transport."}], "type": "PATH", "value": "/opt/ros/noetic/share/image_transport/cmake"}, {"name": "jsoncpp_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for jsoncpp."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/jsoncpp"}, {"name": "lib", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/ros/noetic/lib/libxmlrpcpp.so"}, {"name": "message_filters_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for message_filters."}], "type": "PATH", "value": "/opt/ros/noetic/share/message_filters/cmake"}, {"name": "message_generation_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for message_generation."}], "type": "PATH", "value": "/opt/ros/noetic/share/message_generation/cmake"}, {"name": "message_runtime_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for message_runtime."}], "type": "PATH", "value": "/opt/ros/noetic/share/message_runtime/cmake"}, {"name": "nav_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for nav_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/nav_msgs/cmake"}, {"name": "octomap_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for octomap."}], "type": "PATH", "value": "/opt/ros/noetic/share/octomap"}, {"name": "onelib", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libgazebo_opende_ou.so"}, {"name": "pkgcfg_lib_BULLET_BulletCollision", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libBulletCollision.so"}, {"name": "pkgcfg_lib_BULLET_BulletDynamics", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libBulletDynamics.so"}, {"name": "pkgcfg_lib_BULLET_BulletSoftBody", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libBulletSoftBody.so"}, {"name": "pkgcfg_lib_BULLET_LinearMath", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libLinearMath.so"}, {"name": "pkgcfg_lib_JSONCPP_jsoncpp", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libjsoncpp.so"}, {"name": "pkgcfg_lib_OGRE_PKGC_OgreMain", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOgreMain.so"}, {"name": "pkgcfg_lib_OGRE_PKGC_pthread", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libpthread.so"}, {"name": "pkgcfg_lib_PC_ASSIMP_assimp", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libassimp.so"}, {"name": "pkgcfg_lib_PC_CCD_ccd", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libccd.so"}, {"name": "pkgcfg_lib_PC_CCD_m", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libm.so"}, {"name": "pkgcfg_lib_PC_CURL_curl", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libcurl.so"}, {"name": "pkgcfg_lib_PC_FCL_ccd", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libccd.so"}, {"name": "pkgcfg_lib_PC_FCL_fcl", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/ros/noetic/lib/x86_64-linux-gnu/libfcl.so"}, {"name": "pkgcfg_lib_PC_FCL_m", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libm.so"}, {"name": "pkgcfg_lib_PC_FCL_octomap", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/ros/noetic/lib/liboctomap.so"}, {"name": "pkgcfg_lib_PC_FCL_octomath", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/opt/ros/noetic/lib/liboctomath.so"}, {"name": "pkgcfg_lib_TINYXML2_tinyxml2", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so"}, {"name": "pkgcfg_lib_UUID_uuid", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libuuid.so"}, {"name": "pkgcfg_lib_YAML_yaml", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libyaml.so"}, {"name": "pkgcfg_lib_ZIP_zip", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libzip.so"}, {"name": "pkgcfg_lib_ZeroMQ_zmq", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libzmq.so"}, {"name": "pluginlib_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for pluginlib."}], "type": "PATH", "value": "/opt/ros/noetic/share/pluginlib/cmake"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr//usr/lib/x86_64-linux-gnu"}, {"name": "realsense_gazebo_plugin_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/realsense_gazebo_plugin"}, {"name": "realsense_gazebo_plugin_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;/opt/ros/noetic/lib/libgazebo_ros_api_plugin.so;general;/opt/ros/noetic/lib/libgazebo_ros_paths_plugin.so;general;/usr/lib/x86_64-linux-gnu/libtinyxml.so;general;/opt/ros/noetic/lib/libtf.so;general;/opt/ros/noetic/lib/libtf2_ros.so;general;/opt/ros/noetic/lib/libactionlib.so;general;/opt/ros/noetic/lib/libtf2.so;general;/opt/ros/noetic/lib/libdynamic_reconfigure_config_init_mutex.so;general;/opt/ros/noetic/lib/libimage_transport.so;general;/opt/ros/noetic/lib/libmessage_filters.so;general;/opt/ros/noetic/lib/libclass_loader.so;general;/usr/lib/x86_64-linux-gnu/libPocoFoundation.so;general;/usr/lib/x86_64-linux-gnu/libdl.so;general;/opt/ros/noetic/lib/libroslib.so;general;/opt/ros/noetic/lib/librospack.so;general;/usr/lib/x86_64-linux-gnu/libpython3.8.so;general;/usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libtinyxml2.so;general;/opt/ros/noetic/lib/libcamera_info_manager.so;general;/opt/ros/noetic/lib/libcamera_calibration_parsers.so;general;/opt/ros/noetic/lib/libroscpp.so;general;/usr/lib/x86_64-linux-gnu/libpthread.so;general;/usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0;general;/opt/ros/noetic/lib/librosconsole.so;general;/opt/ros/noetic/lib/librosconsole_log4cxx.so;general;/opt/ros/noetic/lib/librosconsole_backend_interface.so;general;/usr/lib/x86_64-linux-gnu/liblog4cxx.so;general;/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0;general;/opt/ros/noetic/lib/libxmlrpcpp.so;general;/opt/ros/noetic/lib/libroscpp_serialization.so;general;/opt/ros/noetic/lib/librostime.so;general;/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0;general;/opt/ros/noetic/lib/libcpp_common.so;general;/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4;general;BulletSoftBody;general;BulletDynamics;general;BulletCollision;general;LinearMath;general;SimTKcommon;general;SimTKmath;general;SimTKsimbody;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;/usr/lib/x86_64-linux-gnu/libgazebo.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_client.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_gui.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_sensors.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_rendering.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_physics.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_ode.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_transport.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_msgs.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_util.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_common.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_gimpact.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_opcode.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_opende_ou.so;general;Boost::thread;general;Boost::system;general;Boost::filesystem;general;Boost::program_options;general;Boost::regex;general;Boost::iostreams;general;Boost::date_time;general;/usr/lib/x86_64-linux-gnu/libprotobuf.so;general;-lpthread;general;sdformat9::sdformat9;optimized;/usr/lib/x86_64-linux-gnu/libOgreMain.so;debug;/usr/lib/x86_64-linux-gnu/libOgreMain.so;general;Boost::thread;general;Boost::date_time;optimized;/usr/lib/x86_64-linux-gnu/libOgreTerrain.so;debug;/usr/lib/x86_64-linux-gnu/libOgreTerrain.so;optimized;/usr/lib/x86_64-linux-gnu/libOgrePaging.so;debug;/usr/lib/x86_64-linux-gnu/libOgrePaging.so;general;ignition-common3::ignition-common3-graphics;"}, {"name": "realsense_gazebo_plugin_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/realsense_gazebo_plugin"}, {"name": "robot_description_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/robot_description"}, {"name": "robot_description_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/robot_description"}, {"name": "ros_imu_bno055_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/ros_imu_bno055"}, {"name": "ros_imu_bno055_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/ros_imu_bno055"}, {"name": "rosconsole_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for rosconsole."}], "type": "PATH", "value": "/opt/ros/noetic/share/rosconsole/cmake"}, {"name": "roscpp_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for roscpp."}], "type": "PATH", "value": "/opt/ros/noetic/share/roscpp/cmake"}, {"name": "roscpp_serialization_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for roscpp_serialization."}], "type": "PATH", "value": "/opt/ros/noetic/share/roscpp_serialization/cmake"}, {"name": "roscpp_traits_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for roscpp_traits."}], "type": "PATH", "value": "/opt/ros/noetic/share/roscpp_traits/cmake"}, {"name": "rosgraph_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for rosgraph."}], "type": "PATH", "value": "/opt/ros/noetic/share/rosgraph/cmake"}, {"name": "rosgraph_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for rosgraph_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/rosgraph_msgs/cmake"}, {"name": "roslib_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for roslib."}], "type": "PATH", "value": "/opt/ros/noetic/share/roslib/cmake"}, {"name": "rospack_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for rospack."}], "type": "PATH", "value": "/opt/ros/noetic/share/rospack/cmake"}, {"name": "rospy_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for rospy."}], "type": "PATH", "value": "/opt/ros/noetic/share/rospy/cmake"}, {"name": "rostime_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for rostime."}], "type": "PATH", "value": "/opt/ros/noetic/share/rostime/cmake"}, {"name": "sdformat9_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for sdformat9."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/sdformat9"}, {"name": "sensor_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for sensor_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/sensor_msgs/cmake"}, {"name": "std_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for std_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/std_msgs/cmake"}, {"name": "std_srvs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for std_srvs."}], "type": "PATH", "value": "/opt/ros/noetic/share/std_srvs/cmake"}, {"name": "tf2_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for tf2."}], "type": "PATH", "value": "/opt/ros/noetic/share/tf2/cmake"}, {"name": "tf2_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for tf2_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/tf2_msgs/cmake"}, {"name": "tf2_py_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for tf2_py."}], "type": "PATH", "value": "/opt/ros/noetic/share/tf2_py/cmake"}, {"name": "tf2_ros_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for tf2_ros."}], "type": "PATH", "value": "/opt/ros/noetic/share/tf2_ros/cmake"}, {"name": "tf_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for tf."}], "type": "PATH", "value": "/opt/ros/noetic/share/tf/cmake"}, {"name": "trajectory_msgs_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for trajectory_msgs."}], "type": "PATH", "value": "/opt/ros/noetic/share/trajectory_msgs/cmake"}, {"name": "visual_crop_row_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/visual-crop-row"}, {"name": "visual_crop_row_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/visual-crop-row"}, {"name": "visual_servoing_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/visual_servoing"}, {"name": "visual_servoing_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/visual_servoing"}, {"name": "weednix_launch_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/weednix_launch"}, {"name": "weednix_launch_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/weednix_launch"}, {"name": "weednix_sensors_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/build/weednix_sensors"}, {"name": "weednix_sensors_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/weednix_ws/src/weednix_sensors"}, {"name": "xmlrpcpp_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for xmlrpcpp."}], "type": "PATH", "value": "/opt/ros/noetic/share/xmlrpcpp/cmake"}, {"name": "yaml_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for yaml."}], "type": "PATH", "value": "yaml_DIR-NOTFOUND"}], "kind": "cache", "version": {"major": 2, "minor": 0}}