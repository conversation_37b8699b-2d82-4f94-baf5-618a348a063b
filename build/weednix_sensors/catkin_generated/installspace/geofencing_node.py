#!/usr/bin/env python3

import rospy
import json
import numpy as np
import os
from shapely.geometry import Point, Polygon
from sensor_msgs.msg import NavSatFix
from std_msgs.msg import Bool, String
from std_srvs.srv import Trigger, TriggerResponse
from geopy.distance import distance

class GeofencingNode:
    def __init__(self):
        rospy.init_node('geofencing_node')

        # Parameters
        self.geojson_file = rospy.get_param('~geojson_file', 'boundaries.geojson')
        self.gps_error_radius = rospy.get_param('~gps_error_radius', 2.0)  # Error radius in meters
        self.gps_topic = rospy.get_param('~gps_topic', '/gps/fix')
        self.update_rate = rospy.get_param('~update_rate', 50.0)  # Hz

        # Load GeoJSON boundary
        self.boundary_polygon = self.load_boundary()

        # Most recent GPS coordinates
        self.current_lat = None
        self.current_lon = None
        self.is_inside = False
        self.distance_to_boundary = float('inf')

        # Publishers
        self.inside_pub = rospy.Publisher('~inside_boundary', Bool, queue_size=1)
        self.status_pub = rospy.Publisher('~boundary_status', String, queue_size=1)

        # Subscribers
        rospy.Subscriber(self.gps_topic, NavSatFix, self.gps_callback)

        # Timer for periodic checks
        self.timer = rospy.Timer(rospy.Duration(1.0/self.update_rate), self.check_boundary)

        # Service for reloading boundary data
        self.reload_service = rospy.Service('~reload_boundary', Trigger, self.reload_boundary_service)

        # File monitoring for automatic reload
        self.last_modified_time = self.get_file_modified_time()
        self.file_check_timer = rospy.Timer(rospy.Duration(5.0), self.check_file_changes)  # Check every 5 seconds

        rospy.loginfo("Geofencing node initialized with boundary from: %s", self.geojson_file)
        rospy.loginfo("GPS error radius set to: %.2f meters", self.gps_error_radius)
        rospy.loginfo("Boundary reload service available at: %s", rospy.resolve_name('~reload_boundary'))
        # Make sure these match between your GPS plugin and geofencing node
        print("GPS plugin reference: {}, {}".format(
            rospy.get_param('/gazebo_ros_gps/referenceLatitude', 'unknown'),
            rospy.get_param('/gazebo_ros_gps/referenceLongitude', 'unknown')))

    def load_boundary(self):
        """Load boundary polygon from GeoJSON file"""
        try:
            with open(self.geojson_file, 'r') as f:
                geojson_data = json.load(f)

            # Extract coordinates from the first feature in the GeoJSON
            # Assuming the GeoJSON contains a polygon feature
            for feature in geojson_data['features']:
                if feature['geometry']['type'] == 'Polygon':
                    coordinates = feature['geometry']['coordinates'][0]  # First polygon, outer ring
                    # Convert to shapely polygon format (convert from [lon, lat] to [lat, lon])
                    polygon_coords = [(coord[1], coord[0]) for coord in coordinates]
                    return Polygon(polygon_coords)

            rospy.logerr("No polygon found in GeoJSON file")
            return None

        except Exception as e:
            rospy.logerr("Failed to load boundary from GeoJSON: %s", str(e))
            return None

    def gps_callback(self, msg):
        """Callback for GPS messages"""
        self.current_lat = msg.latitude
        self.current_lon = msg.longitude

    def get_distance_to_boundary(self, point, polygon):
        """Calculate minimum distance from point to polygon boundary"""
        if polygon.contains(point):
            # Point is inside, calculate distance to nearest edge
            boundary = polygon.exterior
            return boundary.distance(point)
        else:
            # Point is outside
            return -polygon.exterior.distance(point)

    def check_boundary(self, event=None):
        """Check if current position is within boundary, considering GPS error"""
        if self.current_lat is None or self.current_lon is None:
            rospy.logwarn_throttle(10, "No GPS data received yet")
            return

        if self.boundary_polygon is None:
            rospy.logerr_throttle(10, "No valid boundary polygon loaded")
            return

        # Create point from current GPS coordinates
        current_point = Point(self.current_lat, self.current_lon)

        # Calculate distance to boundary (positive if inside, negative if outside)
        self.distance_to_boundary = self.get_distance_to_boundary(current_point, self.boundary_polygon)

        # Consider a point inside only if it's inside by more than the GPS error radius
        # Convert distance to meters first (it's in degrees)
        lat_lon_distance = abs(self.distance_to_boundary)
        meters_distance = self.convert_degree_distance_to_meters(lat_lon_distance, self.current_lat)

        # Point is inside if distance is positive and greater than error radius
        # or if distance is negative but absolute value less than error radius
        if self.distance_to_boundary > 0:
            if meters_distance > self.gps_error_radius:
                self.is_inside = True
                status_msg = f"Inside boundary by {meters_distance:.2f}m (GPS error: {self.gps_error_radius:.2f}m)"
            else:
                self.is_inside = False
                status_msg = f"Near boundary (inside by {meters_distance:.2f}m) - Within GPS error zone"
        else:
            if meters_distance < self.gps_error_radius:
                self.is_inside = True
                status_msg = f"Near boundary (outside by {meters_distance:.2f}m) - Within GPS error zone"
            else:
                self.is_inside = False
                status_msg = f"Outside boundary by {meters_distance:.2f}m (GPS error: {self.gps_error_radius:.2f}m)"

        # Publish results
        self.inside_pub.publish(Bool(self.is_inside))
        self.status_pub.publish(String(status_msg))

        # Log status (throttled to avoid flooding)
        if self.is_inside:
            rospy.loginfo_throttle(5, status_msg)
        else:
            rospy.logwarn_throttle(5, status_msg)

    def convert_degree_distance_to_meters(self, degree_distance, latitude):
        """Convert a distance in degrees to meters at a given latitude"""
        # We need a reference point to calculate distance
        ref_lat = latitude
        ref_lon = 0.0
        target_lon = degree_distance + ref_lon

        # Calculate the distance in meters
        meters = distance(
            (ref_lat, ref_lon),
            (ref_lat, target_lon)
        ).meters

        return meters

    def get_file_modified_time(self):
        """Get the last modified time of the GeoJSON file"""
        try:
            if os.path.exists(self.geojson_file):
                return os.path.getmtime(self.geojson_file)
            else:
                rospy.logwarn("GeoJSON file does not exist: %s", self.geojson_file)
                return 0
        except Exception as e:
            rospy.logerr("Error getting file modified time: %s", str(e))
            return 0

    def check_file_changes(self, event=None):
        """Check if the GeoJSON file has been modified and reload if necessary"""
        try:
            current_modified_time = self.get_file_modified_time()
            if current_modified_time > self.last_modified_time:
                rospy.loginfo("Detected change in boundary file, reloading...")
                if self.reload_boundary():
                    self.last_modified_time = current_modified_time
                    rospy.loginfo("Boundary data reloaded successfully due to file change")
                else:
                    rospy.logerr("Failed to reload boundary data after file change")
        except Exception as e:
            rospy.logerr("Error checking file changes: %s", str(e))

    def reload_boundary_service(self, req):
        """Service callback to reload boundary data"""
        try:
            success = self.reload_boundary()
            if success:
                self.last_modified_time = self.get_file_modified_time()
                return TriggerResponse(success=True, message="Boundary data reloaded successfully")
            else:
                return TriggerResponse(success=False, message="Failed to reload boundary data")
        except Exception as e:
            rospy.logerr("Error in reload boundary service: %s", str(e))
            return TriggerResponse(success=False, message=f"Error reloading boundary: {str(e)}")

    def reload_boundary(self):
        """Reload boundary data from the GeoJSON file"""
        try:
            # Update geojson_file parameter in case it changed
            new_geojson_file = rospy.get_param('~geojson_file', self.geojson_file)
            if new_geojson_file != self.geojson_file:
                rospy.loginfo("Boundary file parameter changed from %s to %s", self.geojson_file, new_geojson_file)
                self.geojson_file = new_geojson_file

            # Load new boundary
            new_boundary = self.load_boundary()
            if new_boundary is not None:
                self.boundary_polygon = new_boundary
                rospy.loginfo("Successfully reloaded boundary from: %s", self.geojson_file)
                return True
            else:
                rospy.logerr("Failed to load new boundary data")
                return False
        except Exception as e:
            rospy.logerr("Error reloading boundary: %s", str(e))
            return False

if __name__ == '__main__':
    try:
        node = GeofencingNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
