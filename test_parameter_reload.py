#!/usr/bin/env python3

"""
Test script for parameter reloading functionality.
This script tests both the geofencing node and row crop follower parameter reload services.
"""

import rospy
import sys
from std_srvs.srv import Trigger

def test_geofencing_reload():
    """Test the geofencing node boundary reload service"""
    print("Testing geofencing node boundary reload...")
    
    try:
        # Wait for the service to be available
        rospy.wait_for_service('/geofencing_node/reload_boundary', timeout=5.0)
        
        # Create service proxy
        reload_boundary = rospy.ServiceProxy('/geofencing_node/reload_boundary', Trigger)
        
        # Call the service
        response = reload_boundary()
        
        if response.success:
            print(f"✅ Geofencing reload successful: {response.message}")
            return True
        else:
            print(f"❌ Geofencing reload failed: {response.message}")
            return False
            
    except rospy.ServiceException as e:
        print(f"❌ Geofencing service call failed: {e}")
        return False
    except rospy.ROSException as e:
        print(f"❌ Geofencing service not available: {e}")
        return False

def test_row_crop_follower_reload():
    """Test the row crop follower parameter reload service"""
    print("Testing row crop follower parameter reload...")
    
    try:
        # Wait for the service to be available
        rospy.wait_for_service('/row_crop_follower/reload_parameters', timeout=5.0)
        
        # Create service proxy
        reload_params = rospy.ServiceProxy('/row_crop_follower/reload_parameters', Trigger)
        
        # Call the service
        response = reload_params()
        
        if response.success:
            print(f"✅ Row crop follower reload successful: {response.message}")
            return True
        else:
            print(f"❌ Row crop follower reload failed: {response.message}")
            return False
            
    except rospy.ServiceException as e:
        print(f"❌ Row crop follower service call failed: {e}")
        return False
    except rospy.ROSException as e:
        print(f"❌ Row crop follower service not available: {e}")
        return False

def main():
    """Main test function"""
    rospy.init_node('parameter_reload_test', anonymous=True)
    
    print("🧪 Parameter Reload Test Script")
    print("=" * 50)
    
    # Test both services
    geofencing_success = test_geofencing_reload()
    print()
    row_crop_success = test_row_crop_follower_reload()
    
    print()
    print("=" * 50)
    print("📊 Test Results:")
    print(f"Geofencing Node: {'✅ PASS' if geofencing_success else '❌ FAIL'}")
    print(f"Row Crop Follower: {'✅ PASS' if row_crop_success else '❌ FAIL'}")
    
    if geofencing_success and row_crop_success:
        print("🎉 All tests passed! Parameter reloading is working correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check node status and configuration.")
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        print("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test failed with error: {e}")
        sys.exit(1)
